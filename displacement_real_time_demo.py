"""
基于位移数据的实时LSTM+卡尔曼滤波预测演示
专门针对振动位移信号进行实时预测优化
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
import time
from collections import deque
import warnings
import random

def set_all_seeds(seed=42):
    """设置所有随机种子以确保结果可重复"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

# 设置随机种子
set_all_seeds(42)

# 忽略警告
warnings.filterwarnings("ignore")

# 设置中文字体
try:
    # 基于字体测试结果，使用SimHei字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    USE_CHINESE = True
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    USE_CHINESE = False
    print("使用默认字体")

# 位移专用LSTM模型
class DisplacementLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, output_size=1):
        super().__init__()
        self.hidden_size = hidden_size
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.linear = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        # x shape: (batch_size, seq_len, input_size)
        lstm_out, _ = self.lstm(x)
        # 取最后一个时间步的输出
        out = lstm_out[:, -1, :]
        out = self.dropout(out)
        prediction = self.linear(out)
        return prediction

# 平缓振动专用自适应卡尔曼滤波器
class DisplacementKalmanFilter:
    def __init__(self, initial_process_variance=1e-4, initial_measurement_variance=5e-4):
        """
        针对平缓振动信号优化的卡尔曼滤波器
        平缓振动变化相对规律，使用较小的过程噪声和测量噪声方差
        """
        self.Q = initial_process_variance  # 过程噪声方差（平缓振动变化较小）
        self.R = initial_measurement_variance  # 测量噪声方差
        self.x_hat = 0.0  # 状态估计值
        self.P = 1.0      # 估计误差协方差

        # 平缓振动信号特有的参数
        self.velocity_estimate = 0.0  # 速度估计
        self.previous_displacement = 0.0

        # 自适应参数
        self.measurement_history = []
        self.innovation_history = []
        self.max_history = 15  # 增加历史长度以更好地跟踪平缓变化

        # 平缓振动信号的自适应策略
        self.displacement_threshold = 2e-3  # 减小位移变化阈值
        self.adaptation_rate = 0.08  # 减小自适应率以保持稳定性
        
    def predict(self):
        """预测步骤（考虑位移的连续性）"""
        # 基于速度估计进行预测
        self.x_hat = self.x_hat + self.velocity_estimate
        self.P = self.P + self.Q
        
    def update(self, measurement):
        """更新步骤（针对位移信号优化）"""
        # 记录测量历史
        self.measurement_history.append(measurement)
        if len(self.measurement_history) > self.max_history:
            self.measurement_history.pop(0)
        
        # 计算创新
        innovation = measurement - self.x_hat
        self.innovation_history.append(innovation)
        if len(self.innovation_history) > self.max_history:
            self.innovation_history.pop(0)
        
        # 自适应调整（基于位移信号特性）
        self._adaptive_adjustment()
        
        # 卡尔曼增益
        K = self.P / (self.P + self.R)
        
        # 状态更新
        self.x_hat = self.x_hat + K * innovation
        self.P = (1 - K) * self.P
        
        # 更新速度估计
        if len(self.measurement_history) >= 2:
            current_velocity = measurement - self.previous_displacement
            self.velocity_estimate = 0.3 * self.velocity_estimate + 0.7 * current_velocity
        
        self.previous_displacement = measurement
        
        return self.x_hat
    
    def _adaptive_adjustment(self):
        """基于平缓振动信号特性的自适应调整"""
        if len(self.measurement_history) < 3:
            return

        recent_measurements = np.array(self.measurement_history[-3:])

        # 计算位移变化的平缓程度
        displacement_changes = np.abs(np.diff(recent_measurements))
        avg_change = np.mean(displacement_changes)
        max_change = np.max(displacement_changes)

        # 检测变化模式（平缓振动的变化相对规律）
        if max_change > self.displacement_threshold * 2:
            # 检测到较大变化，适度增加过程噪声
            self.Q = min(5e-3, self.Q * 1.2)
        elif avg_change > self.displacement_threshold:
            # 变化适中，轻微增加过程噪声
            self.Q = min(2e-3, self.Q * 1.05)
        else:
            # 变化较小，适度减少过程噪声
            self.Q = max(1e-6, self.Q * 0.99)

        # 基于创新序列调整测量噪声
        if len(self.innovation_history) >= 3:
            innovation_var = np.var(self.innovation_history[-3:])
            if innovation_var > 0:
                target_R = innovation_var * 0.6  # 减小系数以适应平缓振动
                self.R = (1 - self.adaptation_rate) * self.R + self.adaptation_rate * target_R
                self.R = np.clip(self.R, 1e-6, 1e-2)  # 缩小测量噪声范围

# 平缓振动实时预测器
class DisplacementRealTimePredictor:
    def __init__(self, model, scaler, window_size=25, use_kalman=True):
        """
        平缓振动实时预测器
        window_size: 针对平缓振动，使用较大的窗口以更好地捕捉规律性变化
        """
        self.model = model
        self.scaler = scaler
        self.window_size = window_size
        self.use_kalman = use_kalman
        self.model.eval()

        # 数据缓冲区
        self.data_buffer = deque(maxlen=window_size)

        # 卡尔曼滤波器
        if self.use_kalman:
            self.kalman_filter = DisplacementKalmanFilter(
                initial_process_variance=1e-4,  # 平缓振动的过程噪声较小
                initial_measurement_variance=5e-4
            )
        
        # 性能统计
        self.prediction_times = []
        self.predictions_made = 0
        
    def initialize_buffer(self, initial_data):
        """初始化数据缓冲区"""
        if len(initial_data) < self.window_size:
            raise ValueError(f"初始数据长度必须至少为 {self.window_size}")
        
        # 标准化初始数据
        normalized_data = self.scaler.transform(
            np.array(initial_data[-self.window_size:]).reshape(-1, 1)
        ).flatten()
        
        # 填充缓冲区
        self.data_buffer.clear()
        for value in normalized_data:
            self.data_buffer.append(value)
        
        print(f"平缓振动预测器缓冲区已初始化，包含 {len(self.data_buffer)} 个数据点")
    
    def predict_next(self, new_data_point=None):
        """预测下一个位移值"""
        start_time = time.time()
        
        # 如果提供了新数据点，添加到缓冲区
        if new_data_point is not None:
            normalized_point = self.scaler.transform([[new_data_point]])[0, 0]
            self.data_buffer.append(normalized_point)
        
        # 检查缓冲区数据
        if len(self.data_buffer) < self.window_size:
            raise ValueError(f"缓冲区数据不足，需要 {self.window_size} 个点")
        
        # 准备输入序列
        input_seq = torch.FloatTensor(list(self.data_buffer)).unsqueeze(0).unsqueeze(-1)
        
        # LSTM预测
        with torch.no_grad():
            normalized_prediction = self.model(input_seq).item()
        
        # 反标准化
        lstm_prediction = self.scaler.inverse_transform([[normalized_prediction]])[0, 0]
        
        # 卡尔曼滤波优化
        if self.use_kalman:
            self.kalman_filter.predict()
            kalman_prediction = self.kalman_filter.update(lstm_prediction)
        else:
            kalman_prediction = lstm_prediction
        
        # 记录性能
        prediction_time = time.time() - start_time
        self.prediction_times.append(prediction_time)
        self.predictions_made += 1
        
        return lstm_prediction, kalman_prediction, prediction_time
    
    def get_performance_stats(self):
        """获取性能统计"""
        if not self.prediction_times:
            return None
        
        return {
            'total_predictions': self.predictions_made,
            'avg_prediction_time': np.mean(self.prediction_times),
            'max_prediction_time': np.max(self.prediction_times),
            'min_prediction_time': np.min(self.prediction_times),
            'predictions_per_second': 1.0 / np.mean(self.prediction_times)
        }

def create_sequences(data, window_size):
    """创建训练序列"""
    sequences = []
    targets = []
    
    for i in range(len(data) - window_size):
        seq = data[i:i+window_size]
        target = data[i+window_size]
        sequences.append(seq)
        targets.append(target)
    
    return np.array(sequences), np.array(targets)

def displacement_real_time_demo():
    """平缓振动位移数据实时预测演示"""
    print("=== 平缓振动位移数据实时LSTM+卡尔曼滤波预测演示 ===\n")
    print("使用1_2.py生成的1000个点平缓振动数据进行实时预测演示")
    print("数据特征: 多频率成分、渐进性波动、幅度调制、适度噪声")

    # 1. 加载位移数据
    try:
        df = pd.read_csv('smooth_vibration_data.csv')
        displacement_data = df['displacement'].values.astype(float)

        print(f"\n平缓振动位移数据加载成功:")
        print(f"  数据点数: {len(displacement_data)}")
        print(f"  位移范围: {np.min(displacement_data):.4f} ~ {np.max(displacement_data):.4f}")
        print(f"  位移标准差: {np.std(displacement_data):.4f}")
        print(f"  峰峰值: {np.max(displacement_data) - np.min(displacement_data):.4f}")
        print(f"  数据来源: 1_2.py生成的1000个点平缓振动数据")

    except FileNotFoundError:
        print("错误: 未找到 smooth_vibration_data.csv 文件")
        print("请先运行 1_2.py 生成平缓振动数据")
        return
    
    # 2. 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    displacement_normalized = scaler.fit_transform(displacement_data.reshape(-1, 1))
    displacement_normalized = torch.FloatTensor(displacement_normalized.flatten())
    
    # 3. 快速训练平缓振动预测模型
    window_size = 25  # 增大窗口以更好地捕捉平缓振动的规律性
    X, y = create_sequences(displacement_normalized.numpy(), window_size)

    # 转换为PyTorch张量
    X = torch.FloatTensor(X).unsqueeze(-1)  # (batch, seq, feature)
    y = torch.FloatTensor(y)

    # 初始化模型（适中的隐藏层大小处理平缓信号）
    model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.008)  # 适中的学习率

    print(f"\n开始训练平缓振动预测模型...")
    print(f"模型参数: 窗口大小={window_size}, 隐藏层大小=96")

    # 适中的训练轮次处理平缓信号
    epochs = 120
    for epoch in range(epochs):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()

        if epoch % 30 == 0:
            print(f'Epoch {epoch:3d}: Loss = {loss.item():.6f}')

    print("平缓振动模型训练完成！")
    
    # 4. 实时预测演示
    # 使用前80%的数据作为"历史数据"，后20%模拟"实时数据流"
    split_point = int(len(displacement_data) * 0.8)
    historical_data = displacement_data[:split_point]
    streaming_data = displacement_data[split_point:]
    
    print(f"\n=== 实时预测设置 ===")
    print(f"历史数据长度: {len(historical_data)} 点")
    print(f"实时流数据长度: {len(streaming_data)} 点")
    print(f"数据分割点: 80%")
    
    # 创建实时预测器
    predictor = DisplacementRealTimePredictor(model, scaler, window_size)
    predictor.initialize_buffer(historical_data)
    
    # 开始实时预测
    print(f"\n开始平缓振动实时预测...")
    print(f"预测特点: 多频率成分、渐进性波动、幅度调制、适度噪声")

    real_values = []
    lstm_predictions = []
    kalman_predictions = []
    prediction_times = []

    for i, true_value in enumerate(streaming_data):
        # 预测下一个位移值
        lstm_pred, kalman_pred, pred_time = predictor.predict_next()

        # 记录结果
        real_values.append(true_value)
        lstm_predictions.append(lstm_pred)
        kalman_predictions.append(kalman_pred)
        prediction_times.append(pred_time)

        # 将真实观测值添加到预测器
        if i < len(streaming_data) - 1:
            predictor.predict_next(true_value)

        # 显示进度
        if (i + 1) % 50 == 0:  # 增加显示间隔
            print(f"已处理 {i + 1}/{len(streaming_data)} 个位移点，"
                  f"平均预测时间: {np.mean(prediction_times[-50:]):.4f}秒")
    
    return real_values, lstm_predictions, kalman_predictions, predictor

def visualize_displacement_results(real_values, lstm_predictions, kalman_predictions):
    """可视化平缓振动预测结果"""
    print(f"\n生成平缓振动预测结果可视化...")

    plt.figure(figsize=(16, 12))

    time_steps = np.arange(len(real_values))

    # 子图1: 位移预测结果对比
    plt.subplot(3, 1, 1)
    plt.plot(time_steps, real_values, 'b-', label='真实位移' if USE_CHINESE else 'Real Displacement',
             alpha=0.8, linewidth=2)
    plt.plot(time_steps, lstm_predictions, 'r--', label='LSTM预测' if USE_CHINESE else 'LSTM Prediction',
             alpha=0.7, linewidth=1.5)
    plt.plot(time_steps, kalman_predictions, 'g-', label='卡尔曼优化' if USE_CHINESE else 'Kalman Optimized',
             alpha=0.9, linewidth=2)

    plt.title('平缓振动位移实时预测结果对比' if USE_CHINESE else 'Smooth Vibration Real-time Displacement Prediction Comparison',
              fontsize=14, fontweight='bold')
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('位移 (m)' if USE_CHINESE else 'Displacement (m)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    lstm_mae = np.mean(np.abs(np.array(real_values) - np.array(lstm_predictions)))
    kalman_mae = np.mean(np.abs(np.array(real_values) - np.array(kalman_predictions)))

    info_text = f'LSTM MAE: {lstm_mae:.6f}m\nKalman MAE: {kalman_mae:.6f}m'
    plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 子图2: 预测误差分析
    plt.subplot(3, 1, 2)
    lstm_errors = np.abs(np.array(real_values) - np.array(lstm_predictions))
    kalman_errors = np.abs(np.array(real_values) - np.array(kalman_predictions))

    plt.plot(time_steps, lstm_errors, 'r-', alpha=0.7, label='LSTM误差' if USE_CHINESE else 'LSTM Error',
             linewidth=1.5)
    plt.plot(time_steps, kalman_errors, 'g-', alpha=0.9, label='卡尔曼误差' if USE_CHINESE else 'Kalman Error',
             linewidth=2)

    plt.title('位移预测误差对比' if USE_CHINESE else 'Displacement Prediction Error Comparison',
              fontsize=12)
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('绝对误差 (m)' if USE_CHINESE else 'Absolute Error (m)')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图3: 误差改善分析
    plt.subplot(3, 1, 3)
    error_improvement = lstm_errors - kalman_errors
    plt.plot(time_steps, error_improvement, 'purple', alpha=0.8, linewidth=2)
    plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    plt.title('卡尔曼滤波误差改善效果' if USE_CHINESE else 'Kalman Filter Error Improvement',
              fontsize=12)
    plt.xlabel('时间步' if USE_CHINESE else 'Time Steps')
    plt.ylabel('误差减少量 (m)' if USE_CHINESE else 'Error Reduction (m)')
    plt.grid(True, alpha=0.3)

    # 添加改善统计
    avg_improvement = np.mean(error_improvement)
    improvement_text = f'平均改善: {avg_improvement:.6f}m' if USE_CHINESE else f'Avg Improvement: {avg_improvement:.6f}m'
    plt.text(0.02, 0.98, improvement_text, transform=plt.gca().transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

    plt.tight_layout()
    plt.savefig('smooth_vibration_displacement_prediction.png', dpi=300, bbox_inches='tight')
    plt.show()

    print(f"预测结果图像已保存到: smooth_vibration_displacement_prediction.png")

    # 保存详细结果
    results_df = pd.DataFrame({
        'time_step': time_steps,
        'real_displacement': real_values,
        'lstm_prediction': lstm_predictions,
        'kalman_prediction': kalman_predictions,
        'lstm_error': lstm_errors,
        'kalman_error': kalman_errors,
        'error_improvement': error_improvement
    })

    results_df.to_csv('smooth_vibration_displacement_results.csv', index=False)
    print(f"详细结果已保存到: smooth_vibration_displacement_results.csv")

if __name__ == "__main__":
    results = displacement_real_time_demo()
    
    if results is not None:
        real_values, lstm_predictions, kalman_predictions, predictor = results
        
        # 性能分析
        stats = predictor.get_performance_stats()
        
        print(f"\n=== 平缓振动预测性能统计 ===")
        print(f"总预测次数: {stats['total_predictions']}")
        print(f"平均预测时间: {stats['avg_prediction_time']:.4f} 秒")
        print(f"预测速率: {stats['predictions_per_second']:.1f} 次/秒")

        # 准确性分析
        lstm_mae = np.mean(np.abs(np.array(real_values) - np.array(lstm_predictions)))
        lstm_rmse = np.sqrt(np.mean((np.array(real_values) - np.array(lstm_predictions)) ** 2))

        kalman_mae = np.mean(np.abs(np.array(real_values) - np.array(kalman_predictions)))
        kalman_rmse = np.sqrt(np.mean((np.array(real_values) - np.array(kalman_predictions)) ** 2))

        print(f"\n=== 平缓振动预测准确性对比 ===")
        print(f"LSTM原始预测:")
        print(f"  平均绝对误差 (MAE): {lstm_mae:.6f} m")
        print(f"  均方根误差 (RMSE): {lstm_rmse:.6f} m")
        print(f"卡尔曼滤波优化:")
        print(f"  平均绝对误差 (MAE): {kalman_mae:.6f} m")
        print(f"  均方根误差 (RMSE): {kalman_rmse:.6f} m")
        
        mae_improvement = ((lstm_mae - kalman_mae) / lstm_mae) * 100
        rmse_improvement = ((lstm_rmse - kalman_rmse) / lstm_rmse) * 100
        print(f"卡尔曼滤波改善:")
        print(f"  MAE改善: {mae_improvement:.2f}%")
        print(f"  RMSE改善: {rmse_improvement:.2f}%")

        # 可视化结果
        visualize_displacement_results(real_values, lstm_predictions, kalman_predictions)
