"""
测试集成LSTM-卡尔曼滤波器的基本功能
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的模块
from displacement_real_time_demo import DisplacementLSTM, IntegratedLSTMKalmanFilter, IntegratedLSTMKalmanPredictor

def test_integrated_lstm_kalman():
    """测试集成LSTM-卡尔曼滤波器"""
    print("=== 测试集成LSTM-卡尔曼滤波器 ===")
    
    # 1. 生成测试数据
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 生成简单的正弦波数据
    t = np.linspace(0, 4*np.pi, 200)
    displacement_data = 0.1 * np.sin(t) + 0.05 * np.sin(3*t) + 0.02 * np.random.randn(len(t))
    
    print(f"生成测试数据: {len(displacement_data)} 个点")
    print(f"数据范围: {np.min(displacement_data):.4f} ~ {np.max(displacement_data):.4f}")
    
    # 2. 数据预处理
    scaler = MinMaxScaler(feature_range=(-1, 1))
    displacement_normalized = scaler.fit_transform(displacement_data.reshape(-1, 1))
    displacement_normalized = torch.FloatTensor(displacement_normalized.flatten())
    
    # 3. 创建训练数据
    window_size = 20
    def create_sequences(data, window_size):
        sequences = []
        targets = []
        for i in range(len(data) - window_size):
            seq = data[i:i+window_size]
            target = data[i+window_size]
            sequences.append(seq)
            targets.append(target)
        return np.array(sequences), np.array(targets)
    
    X, y = create_sequences(displacement_normalized.numpy(), window_size)
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)
    
    print(f"训练数据形状: X={X.shape}, y={y.shape}")
    
    # 4. 快速训练LSTM模型
    model = DisplacementLSTM(input_size=1, hidden_size=32, output_size=1)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    
    print("开始训练LSTM模型...")
    for epoch in range(50):
        optimizer.zero_grad()
        outputs = model(X).squeeze()
        loss = criterion(outputs, y)
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            print(f'Epoch {epoch}: Loss = {loss.item():.6f}')
    
    print("LSTM模型训练完成!")
    
    # 5. 测试集成LSTM-卡尔曼滤波器
    print("\n测试集成LSTM-卡尔曼滤波器...")
    
    # 分割数据
    split_point = int(len(displacement_data) * 0.8)
    historical_data = displacement_data[:split_point]
    test_data = displacement_data[split_point:]
    
    # 创建集成滤波器
    integrated_filter = IntegratedLSTMKalmanFilter(
        model=model,
        scaler=scaler,
        window_size=window_size,
        initial_process_variance=1e-4,
        initial_measurement_variance=5e-4
    )
    
    # 初始化
    integrated_filter.initialize_buffer(historical_data)
    
    # 测试预测
    predictions = []
    for i, true_value in enumerate(test_data[:10]):  # 只测试前10个点
        # 预测
        pred = integrated_filter.predict_and_update()
        predictions.append(pred)
        
        # 更新（提供观测值）
        if i < len(test_data) - 1:
            integrated_filter.predict_and_update(true_value)
        
        print(f"步骤 {i+1}: 真实值={true_value:.6f}, 预测值={pred:.6f}, 误差={abs(true_value-pred):.6f}")
    
    # 6. 测试集成预测器
    print("\n测试集成LSTM-卡尔曼预测器...")
    
    predictor = IntegratedLSTMKalmanPredictor(
        model=model,
        scaler=scaler,
        window_size=window_size,
        use_integrated_kalman=True
    )
    
    predictor.initialize_buffer(historical_data)
    
    # 测试预测
    for i, true_value in enumerate(test_data[:5]):  # 只测试前5个点
        lstm_pred, kalman_pred, pred_time = predictor.predict_next()
        
        print(f"步骤 {i+1}: 真实值={true_value:.6f}, LSTM={lstm_pred:.6f}, 集成={kalman_pred:.6f}, 时间={pred_time:.4f}s")
        
        # 提供观测值
        if i < len(test_data) - 1:
            predictor.predict_next(true_value)
    
    print("\n=== 测试完成 ===")
    print("集成LSTM-卡尔曼滤波器工作正常!")

if __name__ == "__main__":
    test_integrated_lstm_kalman()
